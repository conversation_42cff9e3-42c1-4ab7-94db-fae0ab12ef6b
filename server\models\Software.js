// server/models/Software.js
const { ObjectId } = require('mongodb');

/**
 * 軟體管理模型
 * 處理軟體資訊的CRUD操作
 */
class Software {
  /**
   * 獲取軟體集合
   * @param {Object} db - MongoDB 數據庫實例
   * @returns {Object} - 軟體集合
   */
  static getCollection(db) {
    return db.collection('software');
  }

  /**
   * 創建新軟體記錄
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} softwareData - 軟體數據
   * @returns {Object} - 創建的軟體記錄
   */
  static async createSoftware(db, softwareData) {
    const collection = this.getCollection(db);

    // 檢查是否已存在相同軟體名稱的軟體
    const existingName = await collection.findOne({
      name: softwareData.name
    });

    if (existingName) {
      throw new Error('軟體名稱已存在，請使用其他軟體名稱');
    }

    // 檢查是否已存在相同版本、設備類型、功能類型的軟體
    const existingVersionDeviceFunction = await collection.findOne({
      version: softwareData.version,
      deviceType: softwareData.deviceType,
      functionType: softwareData.functionType
    });

    if (existingVersionDeviceFunction) {
      throw new Error(`版本 ${softwareData.version} 的 ${softwareData.deviceType} ${softwareData.functionType} 軟體已存在`);
    }

    const software = {
      // 基本資訊
      name: softwareData.name,
      description: softwareData.description || '',
      version: softwareData.version,
      
      // 設備資訊
      deviceType: softwareData.deviceType,
      functionType: softwareData.functionType,
      
      // 檔案資訊
      fileSize: softwareData.fileSize,
      checksum: softwareData.checksum,
      
      // 儲存資訊
      binFileId: softwareData.binFileId,
      extractedBinId: softwareData.extractedBinId,
      
      // 狀態管理
      status: 'active',
      isEnabled: true,
      

      
      // 相容性資訊
      compatibility: softwareData.compatibility || {
        minHardwareVersion: '',
        maxHardwareVersion: '',
        supportedSizes: [],
        requirements: []
      },
      
      // 元數據
      uploadedBy: softwareData.uploadedBy,
      uploadDate: new Date(),
      lastModified: new Date(),
      modifiedBy: softwareData.uploadedBy,
      

      
      // 標籤
      tags: softwareData.tags || [],
      
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await collection.insertOne(software);
    return { ...software, _id: result.insertedId };
  }

  /**
   * 檢查軟體名稱是否已存在
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} name - 軟體名稱
   * @returns {boolean} - 是否已存在
   */
  static async checkNameExists(db, name) {
    const collection = this.getCollection(db);
    const existing = await collection.findOne({ name: name });
    return !!existing;
  }

  /**
   * 檢查軟體版本+設備類型+功能類型組合是否已存在
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} version - 軟體版本
   * @param {string} deviceType - 設備類型
   * @param {string} functionType - 功能類型
   * @returns {boolean} - 是否已存在
   */
  static async checkVersionDeviceFunctionExists(db, version, deviceType, functionType) {
    const collection = this.getCollection(db);
    const existing = await collection.findOne({
      version: version,
      deviceType: deviceType,
      functionType: functionType
    });
    return !!existing;
  }

  /**
   * 查詢軟體列表
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filters - 過濾條件
   * @param {Object} options - 查詢選項
   * @returns {Object} - 查詢結果
   */
  static async findAll(db, filters = {}, options = {}) {
    const collection = this.getCollection(db);
    const { page = 1, limit = 20, search, sortBy = 'uploadDate', sortOrder = -1 } = options;
    
    let query = { ...filters };
    
    // 搜尋功能
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { version: { $regex: search, $options: 'i' } }
      ];
    }

    // 計算總數
    const total = await collection.countDocuments(query);
    
    // 查詢數據
    const software = await collection
      .find(query)
      .sort({ [sortBy]: sortOrder })
      .skip((page - 1) * limit)
      .limit(limit)
      .toArray();

    return { 
      software, 
      total, 
      page: parseInt(page), 
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 根據ID查詢軟體
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 軟體ID
   * @returns {Object} - 軟體記錄
   */
  static async findById(db, id) {
    const collection = this.getCollection(db);
    return await collection.findOne({ _id: new ObjectId(id) });
  }

  /**
   * 更新軟體狀態
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 軟體ID
   * @param {Object} statusData - 狀態數據
   * @param {ObjectId} modifiedBy - 修改者ID
   * @returns {boolean} - 是否更新成功
   */
  static async updateStatus(db, id, statusData, modifiedBy) {
    const collection = this.getCollection(db);
    
    const updateData = {
      ...statusData,
      lastModified: new Date(),
      modifiedBy: modifiedBy,
      updatedAt: new Date()
    };

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );
    
    return result.modifiedCount > 0;
  }

  /**
   * 更新軟體資訊
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 軟體ID
   * @param {Object} updateData - 更新數據
   * @param {ObjectId} modifiedBy - 修改者ID
   * @returns {boolean} - 是否更新成功
   */
  static async updateSoftware(db, id, updateData, modifiedBy) {
    const collection = this.getCollection(db);
    
    const update = {
      ...updateData,
      lastModified: new Date(),
      modifiedBy: modifiedBy,
      updatedAt: new Date()
    };

    const result = await collection.updateOne(
      { _id: new ObjectId(id) },
      { $set: update }
    );
    
    return result.modifiedCount > 0;
  }



  /**
   * 刪除軟體
   * @param {Object} db - MongoDB 數據庫實例
   * @param {string} id - 軟體ID
   * @returns {boolean} - 是否刪除成功
   */
  static async deleteSoftware(db, id) {
    const collection = this.getCollection(db);
    const result = await collection.deleteOne({ _id: new ObjectId(id) });
    return result.deletedCount > 0;
  }

  /**
   * 獲取啟用的軟體列表（供其他頁面選單使用）
   * @param {Object} db - MongoDB 數據庫實例
   * @param {Object} filters - 過濾條件
   * @returns {Array} - 啟用的軟體列表
   */
  static async getEnabledSoftware(db, filters = {}) {
    const collection = this.getCollection(db);
    
    const query = {
      ...filters,
      isEnabled: true,
      status: 'active'
    };

    return await collection
      .find(query, {
        projection: {
          _id: 1,
          name: 1,
          version: 1,
          deviceType: 1,
          functionType: 1,
          fileSize: 1,
          uploadDate: 1
        }
      })
      .sort({ name: 1, version: -1 })
      .toArray();
  }

  /**
   * 獲取軟體統計資訊
   * @param {Object} db - MongoDB 數據庫實例
   * @returns {Object} - 統計資訊
   */
  static async getStatistics(db) {
    const collection = this.getCollection(db);
    
    const stats = await collection.aggregate([
      {
        $group: {
          _id: null,
          totalSoftware: { $sum: 1 },
          enabledSoftware: {
            $sum: { $cond: [{ $eq: ['$isEnabled', true] }, 1, 0] }
          },
          totalSize: { $sum: '$fileSize' }
        }
      },
      {
        $project: {
          _id: 0,
          totalSoftware: 1,
          enabledSoftware: 1,
          disabledSoftware: { $subtract: ['$totalSoftware', '$enabledSoftware'] },
          totalSize: 1
        }
      }
    ]).toArray();

    return stats[0] || {
      totalSoftware: 0,
      enabledSoftware: 0,
      disabledSoftware: 0,
      totalSize: 0
    };
  }
}

module.exports = Software;
