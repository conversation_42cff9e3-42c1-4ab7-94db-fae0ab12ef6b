#!/usr/bin/env python3
"""
Bin檔案格式化工具使用範例
"""

import os
import tempfile
from bin_formatter import BinFormatter

def create_sample_bin():
    """創建一個範例bin檔案用於演示"""
    sample_content = b"""
    This is a sample firmware binary file.
    It contains some mock firmware data for demonstration purposes.
    In real usage, this would be actual compiled firmware code.
    """ * 10  # 重複內容讓檔案更大一些
    
    with tempfile.NamedTemporaryFile(suffix='.bin', delete=False) as f:
        f.write(sample_content)
        return f.name

def demonstrate_usage():
    """演示工具的各種使用方式"""
    print("=== Bin檔案格式化工具使用範例 ===\n")
    
    # 創建範例檔案
    sample_bin = create_sample_bin()
    print(f"創建範例bin檔案: {sample_bin}")
    print(f"檔案大小: {os.path.getsize(sample_bin)} bytes\n")
    
    formatter = BinFormatter()
    
    try:
        # 範例1: Gateway WiFi韌體 (M001型號)
        print("範例1: 格式化Gateway WiFi韌體")
        output1 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="gateway",
            function_type="wifi",
            version="*******",
            device_model=0
        )
        model_name1 = formatter.get_device_model_name("gateway", 0)
        print(f"輸出檔案: {os.path.basename(output1)}")
        print(f"檔案大小: {os.path.getsize(output1)} bytes")
        print(f"裝置型號: {model_name1}\n")

        # 範例2: Gateway BLE韌體 (M001型號)
        print("範例2: 格式化Gateway BLE韌體")
        output2 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="gateway",
            function_type="ble",
            version="2.1.3.5",
            device_model=0
        )
        model_name2 = formatter.get_device_model_name("gateway", 0)
        print(f"輸出檔案: {os.path.basename(output2)}")
        print(f"檔案大小: {os.path.getsize(output2)} bytes")
        print(f"裝置型號: {model_name2}\n")

        # 範例3: EPD BLE韌體 (E001型號)
        print("範例3: 格式化EPD BLE韌體")
        output3 = formatter.format_bin_file(
            bin_path=sample_bin,
            device_type="epd",
            function_type="ble",
            version="0.9.1.2",
            device_model=0
        )
        model_name3 = formatter.get_device_model_name("epd", 0)
        print(f"輸出檔案: {os.path.basename(output3)}")
        print(f"檔案大小: {os.path.getsize(output3)} bytes")
        print(f"裝置型號: {model_name3}\n")

        # 顯示工具信息
        print("工具信息:")
        print(f"支援的設備類型: {', '.join(formatter.get_device_types())}")
        print(f"支援的功能類型: {', '.join(formatter.get_function_types())}")
        print(f"Gateway支援的功能: {', '.join(formatter.get_supported_functions('gateway'))}")
        print(f"EPD支援的功能: {', '.join(formatter.get_supported_functions('epd'))}")

        # 顯示裝置型號信息
        print("\n裝置型號信息:")
        for device_type in formatter.get_device_types():
            models = formatter.get_device_models(device_type)
            print(f"{device_type}: {', '.join([f'{k}={v}' for k, v in models.items()])}")

        print(f"\n所有輸出檔案已儲存到: {formatter.output_dir}")
        
    except Exception as e:
        print(f"錯誤: {e}")
    
    finally:
        # 清理範例檔案
        if os.path.exists(sample_bin):
            os.unlink(sample_bin)

def demonstrate_error_handling():
    """演示錯誤處理"""
    print("\n=== 錯誤處理演示 ===\n")
    
    formatter = BinFormatter()
    sample_bin = create_sample_bin()
    
    error_cases = [
        # (描述, 參數)
        ("不存在的檔案", ("nonexistent.bin", "gateway", "wifi", "*******", 0)),
        ("不支援的設備類型", (sample_bin, "invalid_device", "wifi", "*******", 0)),
        ("不支援的功能類型", (sample_bin, "gateway", "invalid_function", "*******", 0)),
        ("設備功能不匹配", (sample_bin, "epd", "wifi", "*******", 0)),
        ("無效版本格式", (sample_bin, "gateway", "wifi", "1.0.0", 0)),
        ("無效裝置編號", (sample_bin, "gateway", "wifi", "*******", 999)),
    ]
    
    for description, args in error_cases:
        try:
            formatter.format_bin_file(*args)
            print(f"❌ {description}: 應該要失敗但沒有")
        except Exception as e:
            print(f"✅ {description}: {e}")
    
    # 清理
    if os.path.exists(sample_bin):
        os.unlink(sample_bin)

if __name__ == "__main__":
    demonstrate_usage()
    demonstrate_error_handling()
    
    print("\n=== 演示完成 ===")
    print("您可以檢查 output/ 目錄中的生成檔案")
    print("使用 python bin_formatter.py 來互動式使用工具")
