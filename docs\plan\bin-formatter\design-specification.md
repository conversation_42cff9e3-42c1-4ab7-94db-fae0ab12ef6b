# Bin檔案格式化工具 - 詳細設計規範

## 概述

本工具用於將原始韌體bin檔案加上元數據（設備類型、功能類型、版本信息）和校驗和，生成符合系統要求的格式化bin檔案。

## 檔案格式規範

### 整體結構

```
+------------------+------------------+------------------+------------------+------------------+------------------+
| Device Type      | Device Model     | Function Type    | Version Info     | Original Bin     | Checksum         |
| (2 bytes)        | (2 bytes)        | (2 bytes)        | (4 bytes)        | (N bytes)        | (4 bytes)        |
| Little Endian    | Little Endian    | Little Endian    | Little Endian    | Raw Data         | Little Endian    |
+------------------+------------------+------------------+------------------+------------------+------------------+
```

### 各欄位詳細說明

#### 1. 設備類型 (Device Type) - 代號 'a'
- **大小**: 2 bytes
- **格式**: Little Endian
- **用途**: 標識設備類型
- **定義**:
  - 0x0000: Gateway
  - 0x0001: EPD
  - 0x0002-0xFFFF: 保留供未來擴充

#### 2. 裝置編號 (Device Model) - 代號 'b'
- **大小**: 2 bytes
- **格式**: Little Endian
- **用途**: 標識具體的裝置型號
- **定義**:
  - Gateway:
    - 0x0000: M001
  - EPD:
    - 0x0000: E001
  - 0x0001-0xFFFF: 保留供未來擴充

#### 3. 功能類型 (Function Type) - 代號 'c'
- **大小**: 2 bytes
- **格式**: Little Endian
- **用途**: 標識韌體功能類型
- **定義**:
  - 0x0000: WiFi
  - 0x0001: BLE
  - 0x0002-0xFFFF: 保留供未來擴充

#### 4. 版本信息 (Version Info) - 代號 'd'
- **大小**: 4 bytes
- **格式**: Little Endian，每個版本號佔1 byte
- **用途**: 韌體版本標識
- **格式**: x.x.x.x (例如: 1.2.3.4)
- **範例**: 版本 "1.2.3.4" 儲存為 [0x01, 0x02, 0x03, 0x04]

#### 5. 原始Bin檔案 (Original Bin) - 代號 'e'
- **大小**: 可變長度
- **格式**: 原始二進制數據
- **用途**: 實際的韌體代碼

#### 6. 校驗和 (Checksum) - 代號 'f'
- **大小**: 4 bytes
- **格式**: Little Endian
- **用途**: 數據完整性驗證
- **算法**: CRC32，僅對原始bin檔案內容計算

## 設備功能支援矩陣

| 設備類型 | WiFi | BLE | 未來功能 |
|----------|------|-----|----------|
| Gateway  | ✓    | ✓   | 可擴充   |
| EPD      | ✗    | ✓   | 可擴充   |
| 未來設備 | 可配置| 可配置| 可配置   |

## 工具架構設計

### 類別結構

```python
class BinFormatter:
    - __init__()
    - ensure_output_dir()
    - validate_version_format(version: str) -> bool
    - parse_version(version: str) -> bytes
    - calculate_checksum(data: bytes) -> bytes
    - validate_device_function_support(device_type: str, function_type: str) -> bool
    - format_bin_file(bin_path: str, device_type: str, function_type: str, version: str) -> str
    - get_device_types() -> List[str]
    - get_function_types() -> List[str]
    - get_supported_functions(device_type: str) -> List[str]
```

### 配置數據結構

```python
# 設備類型映射
DEVICE_TYPES = {
    'gateway': 0,
    'epd': 1
}

# 裝置編號映射
DEVICE_MODELS = {
    'gateway': {
        0: 'M001'
    },
    'epd': {
        0: 'E001'
    }
}

# 功能類型映射
FUNCTION_TYPES = {
    'wifi': 0,
    'ble': 1
}

# 設備功能支援表
DEVICE_FUNCTION_SUPPORT = {
    'gateway': ['wifi', 'ble'],
    'epd': ['ble']
}
```

## 處理流程

### 主要處理步驟

1. **輸入驗證**
   - 檢查bin檔案是否存在
   - 驗證設備類型是否支援
   - 驗證功能類型是否支援
   - 檢查設備是否支援指定功能
   - 驗證版本格式

2. **數據準備**
   - 讀取原始bin檔案
   - 轉換設備類型為2字節little endian
   - 轉換功能類型為2字節little endian
   - 解析版本字符串為4字節數組

3. **校驗和計算**
   - 使用CRC32算法
   - 僅對原始bin檔案內容計算
   - 轉換為4字節little endian格式

4. **檔案組合**
   - 按照格式規範組合各部分
   - 生成帶時間戳的檔案名稱
   - 寫入輸出目錄

### 錯誤處理

- **FileNotFoundError**: bin檔案不存在
- **ValueError**: 參數格式錯誤或不支援的組合
- **IOError**: 檔案讀寫錯誤

## 擴充性設計

### 新增設備類型

1. 在 `DEVICE_TYPES` 字典中新增映射
2. 在 `DEVICE_FUNCTION_SUPPORT` 中定義支援的功能
3. 無需修改核心邏輯

### 新增功能類型

1. 在 `FUNCTION_TYPES` 字典中新增映射
2. 更新相關設備的支援列表
3. 無需修改核心邏輯

### 版本格式擴充

如需支援其他版本格式，可修改：
- `validate_version_format()` 方法
- `parse_version()` 方法
- 保持4字節輸出格式不變

## 安全考量

1. **輸入驗證**: 嚴格驗證所有輸入參數
2. **檔案安全**: 檢查檔案路徑，防止路徑遍歷攻擊
3. **記憶體管理**: 適當處理大檔案，避免記憶體溢出
4. **錯誤處理**: 提供清晰的錯誤信息，不洩露敏感信息

## 測試策略

### 單元測試
- 各個方法的功能測試
- 邊界條件測試
- 錯誤情況測試

### 整合測試
- 完整流程測試
- 不同設備和功能組合測試
- 檔案格式驗證測試

### 效能測試
- 大檔案處理測試
- 記憶體使用測試
- 處理速度測試

## 使用場景

1. **開發階段**: 開發人員打包韌體
2. **測試階段**: QA人員驗證韌體
3. **生產階段**: 自動化構建系統
4. **維護階段**: 韌體更新和修復
