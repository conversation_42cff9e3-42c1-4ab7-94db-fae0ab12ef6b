const BinFileParser = require('./server/services/BinFileParser');
const fs = require('fs');

// 讀取剛生成的測試檔案
const files = fs.readdirSync('./tools/bin-formatter/output');
const testFile = files.find(f => f.includes('gateway_wifi'));

if (testFile) {
  const buffer = fs.readFileSync('./tools/bin-formatter/output/' + testFile);
  const parser = new BinFileParser();
  
  try {
    const result = parser.parseBinFile(buffer);
    console.log('解析結果:');
    console.log('設備類型:', result.deviceType);
    console.log('裝置編號:', result.deviceModel);
    console.log('裝置型號:', result.deviceModelName);
    console.log('功能類型:', result.functionType);
    console.log('版本:', result.version);
    console.log('校驗和驗證:', result.isValid ? '通過' : '失敗');
    console.log('檔案大小:', result.totalSize, 'bytes');
    console.log('韌體大小:', result.binSize, 'bytes');
  } catch (error) {
    console.error('解析錯誤:', error.message);
  }
} else {
  console.log('找不到測試檔案');
}
