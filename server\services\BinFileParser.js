// server/services/BinFileParser.js
const zlib = require('zlib');

/**
 * Bin檔案解析服務
 * 用於解析格式化的bin檔案，提取設備類型、功能類型、版本等資訊
 */
class BinFileParser {
  constructor() {
    // 設備類型映射
    this.DEVICE_TYPE_NAMES = {
      0: 'gateway',
      1: 'epd'
    };

    // 裝置型號映射
    this.DEVICE_MODEL_NAMES = {
      'gateway': {
        0: 'GW-001'
      },
      'epd': {
        0: 'EPD-001'
      }
    };

    // 功能類型映射
    this.FUNCTION_TYPE_NAMES = {
      0: 'wifi',
      1: 'ble'
    };

    // 設備功能支援矩陣
    this.DEVICE_FUNCTION_SUPPORT = {
      'gateway': ['wifi', 'ble'],
      'epd': ['ble']
    };
  }

  /**
   * 計算CRC32校驗和
   * 提供備用實現以確保相容性
   * @param {Buffer} data - 要計算校驗和的數據
   * @returns {number} - CRC32校驗和
   */
  calculateCRC32(data) {
    try {
      // 嘗試使用 Node.js 內建的 zlib.crc32
      if (typeof zlib.crc32 === 'function') {
        return zlib.crc32(data) >>> 0;
      }
    } catch (error) {
      console.warn('zlib.crc32 不可用，使用備用實現:', error.message);
    }

    // 備用 CRC32 實現
    return this.fallbackCRC32(data);
  }

  /**
   * 備用 CRC32 實現
   * 基於標準 CRC32 算法
   * @param {Buffer} data - 要計算校驗和的數據
   * @returns {number} - CRC32校驗和
   */
  fallbackCRC32(data) {
    const CRC32_TABLE = this.generateCRC32Table();
    let crc = 0xFFFFFFFF;

    for (let i = 0; i < data.length; i++) {
      const byte = data[i];
      crc = (crc >>> 8) ^ CRC32_TABLE[(crc ^ byte) & 0xFF];
    }

    return (crc ^ 0xFFFFFFFF) >>> 0;
  }

  /**
   * 生成 CRC32 查找表
   * @returns {Array} - CRC32 查找表
   */
  generateCRC32Table() {
    if (this._crc32Table) {
      return this._crc32Table;
    }

    const table = [];
    for (let i = 0; i < 256; i++) {
      let crc = i;
      for (let j = 0; j < 8; j++) {
        if (crc & 1) {
          crc = (crc >>> 1) ^ 0xEDB88320;
        } else {
          crc = crc >>> 1;
        }
      }
      table[i] = crc >>> 0;
    }

    this._crc32Table = table;
    return table;
  }

  /**
   * 解析bin檔案
   * @param {Buffer} buffer - bin檔案緩衝區
   * @returns {Object} - 解析結果
   */
  parseBinFile(buffer) {
    if (!Buffer.isBuffer(buffer)) {
      throw new Error('輸入必須是Buffer類型');
    }

    if (buffer.length < 22) {
      throw new Error('檔案太小，不是有效的格式化bin檔案');
    }

    try {
      // 解析標頭資訊
      const deviceType = buffer.readUInt16LE(0);
      const deviceModel = buffer.readUInt16LE(2);
      const functionType = buffer.readUInt16LE(4);
      const version = [
        buffer.readUInt8(6),
        buffer.readUInt8(7),
        buffer.readUInt8(8),
        buffer.readUInt8(9)
      ].join('.');

      const minUpgradeVersion = [
        buffer.readUInt8(10),
        buffer.readUInt8(11),
        buffer.readUInt8(12),
        buffer.readUInt8(13)
      ].join('.');

      const maxUpgradeVersion = [
        buffer.readUInt8(14),
        buffer.readUInt8(15),
        buffer.readUInt8(16),
        buffer.readUInt8(17)
      ].join('.');

      // 提取bin數據和校驗和
      const binData = buffer.subarray(18, -4);
      const checksum = buffer.readUInt32LE(buffer.length - 4);

      // 驗證校驗和
      const calculatedChecksum = this.calculateCRC32(binData);
      const isValid = checksum === calculatedChecksum;

      if (!isValid) {
        throw new Error(`CRC校驗和驗證失敗: 期望 ${checksum.toString(16).toUpperCase()}, 實際 ${calculatedChecksum.toString(16).toUpperCase()}`);
      }

      // 轉換設備和功能類型名稱
      const deviceTypeName = this.DEVICE_TYPE_NAMES[deviceType];
      const functionTypeName = this.FUNCTION_TYPE_NAMES[functionType];
      const deviceModelName = this.getDeviceModelName(deviceTypeName, deviceModel);

      if (!deviceTypeName) {
        throw new Error(`不支援的設備類型: ${deviceType}`);
      }

      if (!functionTypeName) {
        throw new Error(`不支援的功能類型: ${functionType}`);
      }

      return {
        deviceType: deviceTypeName,
        deviceModel: deviceModel,
        deviceModelName: deviceModelName,
        functionType: functionTypeName,
        version,
        minUpgradeVersion,
        maxUpgradeVersion,
        binData,
        checksum: checksum.toString(16).toUpperCase().padStart(8, '0'),
        calculatedChecksum: calculatedChecksum.toString(16).toUpperCase().padStart(8, '0'),
        isValid,
        binSize: binData.length,
        totalSize: buffer.length,
        headerSize: 18,
        checksumSize: 4
      };
    } catch (error) {
      if (error.message.includes('CRC') || error.message.includes('不支援')) {
        throw error;
      }
      throw new Error(`解析bin檔案時發生錯誤: ${error.message}`);
    }
  }

  /**
   * 提取純bin內容
   * @param {Buffer} buffer - 原始bin檔案緩衝區
   * @returns {Buffer} - 純bin內容
   */
  extractPureBin(buffer) {
    const parsed = this.parseBinFile(buffer);
    return parsed.binData;
  }

  /**
   * 驗證設備功能支援
   * @param {string} deviceType - 設備類型
   * @param {string} functionType - 功能類型
   * @returns {boolean} - 是否支援
   */
  validateDeviceFunctionSupport(deviceType, functionType) {
    const supportedFunctions = this.DEVICE_FUNCTION_SUPPORT[deviceType];
    return supportedFunctions && supportedFunctions.includes(functionType);
  }

  /**
   * 驗證版本格式
   * @param {string} version - 版本字符串
   * @returns {boolean} - 是否為有效格式
   */
  validateVersionFormat(version) {
    const versionRegex = /^\d+\.\d+\.\d+\.\d+$/;
    return versionRegex.test(version);
  }

  /**
   * 比較版本號
   * @param {string} version1 - 版本1
   * @param {string} version2 - 版本2
   * @returns {number} - 比較結果 (-1: v1<v2, 0: v1=v2, 1: v1>v2)
   */
  compareVersions(version1, version2) {
    const v1Parts = version1.split('.').map(Number);
    const v2Parts = version2.split('.').map(Number);

    for (let i = 0; i < 4; i++) {
      if (v1Parts[i] < v2Parts[i]) return -1;
      if (v1Parts[i] > v2Parts[i]) return 1;
    }
    return 0;
  }

  /**
   * 獲取支援的設備類型列表
   * @returns {Array} - 設備類型列表
   */
  getSupportedDeviceTypes() {
    return Object.values(this.DEVICE_TYPE_NAMES);
  }

  /**
   * 獲取支援的功能類型列表
   * @returns {Array} - 功能類型列表
   */
  getSupportedFunctionTypes() {
    return Object.values(this.FUNCTION_TYPE_NAMES);
  }

  /**
   * 獲取指定設備支援的功能列表
   * @param {string} deviceType - 設備類型
   * @returns {Array} - 支援的功能列表
   */
  getSupportedFunctions(deviceType) {
    return this.DEVICE_FUNCTION_SUPPORT[deviceType] || [];
  }

  /**
   * 獲取裝置型號名稱
   * @param {string} deviceType - 設備類型
   * @param {number} deviceModel - 裝置編號
   * @returns {string} - 裝置型號名稱
   */
  getDeviceModelName(deviceType, deviceModel) {
    const models = this.DEVICE_MODEL_NAMES[deviceType];
    if (!models) {
      return `Unknown_${deviceModel}`;
    }
    return models[deviceModel] || `Unknown_${deviceModel}`;
  }

  /**
   * 獲取指定設備的所有型號
   * @param {string} deviceType - 設備類型
   * @returns {Object} - 型號映射表
   */
  getDeviceModels(deviceType) {
    return this.DEVICE_MODEL_NAMES[deviceType] || {};
  }

  /**
   * 獲取所有設備型號
   * @returns {Object} - 所有設備型號映射表
   */
  getAllDeviceModels() {
    return this.DEVICE_MODEL_NAMES;
  }

  /**
   * 驗證bin檔案是否為格式化檔案
   * @param {Buffer} buffer - bin檔案緩衝區
   * @returns {boolean} - 是否為格式化檔案
   */
  isFormattedBinFile(buffer) {
    try {
      const result = this.parseBinFile(buffer);
      return result.isValid;
    } catch (error) {
      return false;
    }
  }

  /**
   * 獲取檔案資訊摘要
   * @param {Buffer} buffer - bin檔案緩衝區
   * @returns {Object} - 檔案資訊摘要
   */
  getFileInfo(buffer) {
    try {
      const parsed = this.parseBinFile(buffer);
      return {
        isValid: parsed.isValid,
        deviceType: parsed.deviceType,
        deviceModel: parsed.deviceModel,
        deviceModelName: parsed.deviceModelName,
        functionType: parsed.functionType,
        version: parsed.version,
        minUpgradeVersion: parsed.minUpgradeVersion,
        maxUpgradeVersion: parsed.maxUpgradeVersion,
        totalSize: parsed.totalSize,
        binSize: parsed.binSize,
        checksum: parsed.checksum,
        isSupported: this.validateDeviceFunctionSupport(parsed.deviceType, parsed.functionType)
      };
    } catch (error) {
      return {
        isValid: false,
        error: error.message
      };
    }
  }

  /**
   * 生成檔案摘要報告
   * @param {Buffer} buffer - bin檔案緩衝區
   * @param {string} filename - 檔案名稱
   * @returns {Object} - 摘要報告
   */
  generateSummaryReport(buffer, filename) {
    const info = this.getFileInfo(buffer);
    
    if (!info.isValid) {
      return {
        filename,
        status: 'error',
        message: info.error || '檔案格式無效',
        details: null
      };
    }

    const status = info.isSupported ? 'valid' : 'unsupported';
    const message = info.isSupported 
      ? '檔案驗證成功，可以上傳' 
      : `${info.deviceType} 不支援 ${info.functionType} 功能`;

    return {
      filename,
      status,
      message,
      details: {
        deviceType: info.deviceType,
        deviceModel: info.deviceModel,
        deviceModelName: info.deviceModelName,
        functionType: info.functionType,
        version: info.version,
        totalSize: info.totalSize,
        binSize: info.binSize,
        checksum: info.checksum,
        compressionRatio: ((info.totalSize - info.binSize) / info.totalSize * 100).toFixed(2) + '%'
      }
    };
  }
}

module.exports = BinFileParser;
